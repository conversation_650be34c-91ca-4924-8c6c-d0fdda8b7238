"use client"

import React, { useEffect, useRef, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { ChevronDown, ChevronUp, Filter, Search, X } from "lucide-react"
import { toast } from "react-toastify"

import AgentC<PERSON> from "@/components/agent-card"
import AgentSkeleton from "@/components/agent-skeleton"
import { CustomCheckbox } from "@/components/custom-checkbox"
import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { CheckboxGroup } from "@nextui-org/checkbox"
import { Chip } from "@nextui-org/chip"
import { Prisma } from "@prisma/client"

type AgentWithBadge = Prisma.AgentGetPayload<{
  select: {
    id: true
    icon: true
    title: true
    description: true
    badge: true
  }
}>

const Agents = () => {
  const [groupSelected, setGroupSelected] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [page, setPage] = useState(1)
  const [allAgents, setAllAgents] = useState<AgentWithBadge[]>([])
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [showFavorites, setShowFavorites] = useState(false)
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const observerTarget = useRef<HTMLDivElement>(null)
  const pageSize = 8

  const searchParams = useSearchParams()
  const router = useRouter()
  const session = useSession()
  const user = session.data?.user
  const userFavoriteAgentsIds = user?.favoriteAgentIds || []
  const isAuthenticated = !!session.data?.user

  // Utiliser les procédures appropriées en fonction de l'état d'authentification
  const { data: badges, isLoading: isBadgeLoading } = isAuthenticated
    ? trpc.badge.getForFilterAuthenticated.useQuery()
    : trpc.badge.getForFilter.useQuery()

  const { data: agentData, isLoading: isAgentLoading } = isAuthenticated
    ? trpc.agent.getAllAuthenticated.useQuery({ page, pageSize, withBadge: true })
    : trpc.agent.getAll.useQuery({ page, pageSize, withBadge: true })

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString())
    if (params.get("not_authorized") === "true") {
      toast.error("Vous n'êtes pas autorisé!")
      params.delete("not_authorized")
      router.push(`?${params.toString()}`, { scroll: false })
    }
  }, [searchParams, router])

  useEffect(() => {
    if (agentData?.data) {
      setAllAgents((prev) => (page === 1 ? agentData.data : [...prev, ...agentData.data]))
      setHasMore(page < (agentData.pagination?.totalPages || 1))
      setIsLoadingMore(false)
    }

    // Si l'utilisateur doit sélectionner des catégories, afficher un message
    if (agentData?.needsCategorySelection && session.data?.user) {
      // Le composant CategorySelectionCheck sur la page d'accueil s'occupera d'ouvrir le modal
      setAllAgents([]) // Vider la liste des agents pour forcer l'utilisateur à faire une sélection
      setHasMore(false)
    }
  }, [agentData, page, session.data?.user])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore && !isAgentLoading) {
          setIsLoadingMore(true)
          setPage((prev) => prev + 1)
        }
      },
      { threshold: 1.0 }
    )

    if (observerTarget.current) {
      observer.observe(observerTarget.current)
    }

    return () => observer.disconnect()
  }, [hasMore, isLoadingMore, isAgentLoading])

  // useEffect(() => {
  //   setPage(1)
  //   setAllAgents([])
  //   setHasMore(true)
  // }, [groupSelected, showFavorites])

  // Les agents sont déjà filtrés par les catégories de l'utilisateur côté serveur si nécessaire

  // Filtrer les agents en fonction des critères de l'interface utilisateur uniquement
  const filteredAgents = allAgents.filter((agent) => {
    // Filtrer par favoris si l'option est activée
    if (showFavorites && !userFavoriteAgentsIds.includes(agent.id)) return false

    // Filtrer par catégories sélectionnées dans l'interface
    const matchesCategory =
      groupSelected.length === 0 || // Si aucune catégorie n'est sélectionnée, montrer tous les agents
      groupSelected.includes(String(agent.badge?.id)) // Sinon, filtrer par catégorie

    // Filtrer par terme de recherche
    const lowerSearch = searchTerm.toLowerCase()
    const matchesSearch =
      agent.title.toLowerCase().includes(lowerSearch) || agent.description.toLowerCase().includes(lowerSearch)

    return matchesCategory && matchesSearch
  })

  const clearFilters = () => {
    setGroupSelected([])
    setSearchTerm("")
  }

  const getActiveFilterCount = () => {
    let count = groupSelected.length
    if (showFavorites) count++
    return count
  }

  return (
    <div className="flex w-full gap-8">
      {/* Desktop sidebar filter */}
      <div className="sticky top-24 h-fit w-[250px] shrink-0 rounded-lg border border-default-200 bg-background p-4 shadow-sm max-lg:hidden">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold">Filtres</h3>
            <button
              type="button"
              className="text-sm font-light text-default transition-colors hover:text-default-600"
              onClick={clearFilters}
            >
              Tout effacer
            </button>
          </div>

          <hr className="w-full" />

          {userFavoriteAgentsIds.length > 0 && (
            <div className="space-y-2">
              <div className="text-base font-medium">Favoris</div>
              <CustomCheckbox isSelected={showFavorites} onChange={() => setShowFavorites(!showFavorites)}>
                Uniquement les favoris
              </CustomCheckbox>
            </div>
          )}

          <div className="space-y-2">
            <div className="text-base font-medium">Badges</div>
            {isBadgeLoading && <div className="h-10 w-full animate-pulse rounded-md bg-default-300" />}
            {badges && (
              <CheckboxGroup className="gap-2" orientation="vertical" value={groupSelected} onChange={setGroupSelected}>
                {badges.map((badge) => (
                  <CustomCheckbox key={badge.id} value={String(badge.id)}>
                    {badge.title}
                  </CustomCheckbox>
                ))}
              </CheckboxGroup>
            )}
          </div>
        </div>
      </div>

      <div className="grow">
        {/* Header with search and filter button */}
        <div className="sticky top-20 z-20 bg-background/90 p-4 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <div className="relative grow">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <Search className="size-5 text-default" />
              </div>
              <input
                type="text"
                className="w-full rounded-lg border border-default bg-background/50 py-2 pl-10 pr-4 outline-none backdrop-blur-sm transition-all duration-200 ease-in-out placeholder:text-default focus:border-transparent focus:ring-2 focus:ring-primary"
                placeholder="Rechercher un agent..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button onClick={() => setSearchTerm("")} className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <X className="size-4 text-default-500" />
                </button>
              )}
            </div>

            {/* Mobile filter toggle button */}
            <Button className="lg:hidden" variant="flat" onPress={() => setShowMobileFilters(!showMobileFilters)}>
              <Filter className="mr-1 size-4" />
              Filtres
              {getActiveFilterCount() > 0 && (
                <Chip color="primary" className="ml-1">
                  {getActiveFilterCount()}
                </Chip>
              )}
              {showMobileFilters ? <ChevronUp className="ml-1 size-4" /> : <ChevronDown className="ml-1 size-4" />}
            </Button>
          </div>

          {/* Mobile filters panel */}
          {showMobileFilters && (
            <div className="mt-4 rounded-lg border border-default-200 bg-background p-4 shadow-sm lg:hidden">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-base font-medium">Filtres</h3>
                  <Button
                    type="button"
                    variant="light"
                    // className="text-sm font-light text-default transition-colors hover:text-default-600"
                    onPress={clearFilters}
                  >
                    Tout effacer
                  </Button>
                </div>

                {userFavoriteAgentsIds.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Favoris</div>
                    <CustomCheckbox isSelected={showFavorites} onChange={() => setShowFavorites(!showFavorites)}>
                      Uniquement les favoris
                    </CustomCheckbox>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="text-sm font-medium">Badges</div>
                  {isBadgeLoading && <div className="h-10 w-full animate-pulse rounded-md bg-default-300" />}
                  {badges && (
                    <CheckboxGroup
                      className="flex flex-wrap gap-2"
                      orientation="horizontal"
                      value={groupSelected}
                      onChange={setGroupSelected}
                    >
                      {badges.map((badge) => (
                        <CustomCheckbox key={badge.id} value={String(badge.id)}>
                          {badge.title}
                        </CustomCheckbox>
                      ))}
                    </CheckboxGroup>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Active filters display */}
          {(groupSelected.length > 0 || showFavorites) && (
            <div className="mt-3 flex flex-wrap gap-2">
              {showFavorites && (
                <Chip onClose={() => setShowFavorites(false)} variant="flat" color="primary" size="sm">
                  Favoris
                </Chip>
              )}

              {badges &&
                groupSelected.map((id) => {
                  const badge = badges.find((b) => String(b.id) === id)
                  return badge ? (
                    <Chip
                      key={id}
                      onClose={() => setGroupSelected((prev) => prev.filter((i) => i !== id))}
                      variant="flat"
                      color="primary"
                      size="sm"
                    >
                      {badge.title}
                    </Chip>
                  ) : null
                })}
            </div>
          )}
        </div>

        {/* Message de sélection de catégories requise */}
        {agentData?.needsCategorySelection && session.data?.user ? (
          <div className="my-12 text-center">
            <p className="text-lg font-medium">Sélection de catégories requise</p>
            <p className="mt-2 text-sm text-default-600">
              Vous devez sélectionner des catégories pour voir les agents disponibles.
              <br />
              Utilisez le bouton de sélection de catégories dans votre profil pour faire votre choix.
            </p>
          </div>
        ) : (
          <>
            {/* Agent grid */}
            <div className="mt-4 grid grid-cols-1 gap-6 px-4 sm:grid-cols-2 md:grid-cols-3">
              {filteredAgents.map((agent, index) => (
                <AgentCard key={agent.id} agent={agent} index={index} />
              ))}
              {(isAgentLoading || isLoadingMore) &&
                new Array(pageSize).fill(0).map((_, i) => <AgentSkeleton key={i} />)}
            </div>

            <div ref={observerTarget} className="h-10 w-full" style={{ visibility: hasMore ? "visible" : "hidden" }} />

            {filteredAgents.length === 0 && !isAgentLoading && !isLoadingMore && !agentData?.needsCategorySelection && (
              <div className="my-12 text-center">
                <p className="text-lg font-medium">Aucun agent trouvé</p>
                <p className="mt-2 text-sm text-default-600">Essayez de modifier vos filtres</p>
                <Button className="mt-4" variant="flat" onPress={clearFilters}>
                  Réinitialiser les filtres
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default Agents
