import { NextRequest, NextResponse } from "next/server"
import { createHmac } from "crypto"
import { z } from "zod"

import { sendRenewalLinkEmailFromSubscription } from "@/lib/email/renewal-link"
import { env } from "@/lib/env"
import { logger } from "@coheadcoaching/lib"
import { BillingPeriod } from "@prisma/client"

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // Max 10 requests per minute per IP

// Request schema for validation
const renewalLinkEmailRequestSchema = z.object({
  userEmail: z.string().email(),
  userName: z.string(),
  userLocale: z.string().optional(),
  token: z.string(),
  subscription: z.object({
    id: z.string(),
    plan: z.object({
      name: z.string(),
    }),
    billingPeriod: z.nativeEnum(BillingPeriod),
  }),
  expiresAt: z.string().datetime(),
  timestamp: z.number(),
})

type RenewalLinkEmailRequest = z.infer<typeof renewalLinkEmailRequestSchema>

function verifySignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = createHmac("sha256", secret).update(payload).digest("hex")
  const providedSignature = signature.replace("sha256=", "")

  // Use constant-time comparison to prevent timing attacks
  if (expectedSignature.length !== providedSignature.length) {
    return false
  }

  let result = 0
  for (let i = 0; i < expectedSignature.length; i++) {
    result |= expectedSignature.charCodeAt(i) ^ providedSignature.charCodeAt(i)
  }

  return result === 0
}

function checkRateLimit(clientIp: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIp)

  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize rate limit for this IP
    rateLimitStore.set(clientIp, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  return true
}

function getClientIp(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get("x-forwarded-for")
  const realIp = request.headers.get("x-real-ip")
  const cfConnectingIp = request.headers.get("cf-connecting-ip")

  if (forwarded) {
    return forwarded.split(",")[0].trim()
  }

  return realIp || cfConnectingIp || "unknown"
}

export async function POST(request: NextRequest) {
  try {
    // Check if email service is enabled
    if (!env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE) {
      logger.warn("Email service is disabled, rejecting renewal link email webhook")
      return NextResponse.json({ error: "Email service is disabled" }, { status: 503 })
    }

    // Get client IP for rate limiting
    const clientIp = getClientIp(request)

    // Check rate limit
    if (!checkRateLimit(clientIp)) {
      logger.warn(`Rate limit exceeded for IP: ${clientIp}`)
      return NextResponse.json({ error: "Rate limit exceeded" }, { status: 429 })
    }

    // Verify API key
    const apiKey = request.headers.get("x-api-key")
    const expectedApiKey = env.EMAIL_WEBHOOK_API_KEY

    if (!expectedApiKey) {
      logger.error("EMAIL_WEBHOOK_API_KEY not configured")
      return NextResponse.json({ error: "Webhook not properly configured" }, { status: 500 })
    }

    if (!apiKey || apiKey !== expectedApiKey) {
      logger.warn(`Invalid API key provided from IP: ${clientIp}`)
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get and verify signature
    const signature = request.headers.get("x-signature")
    if (!signature) {
      logger.warn(`Missing signature from IP: ${clientIp}`)
      return NextResponse.json({ error: "Missing signature" }, { status: 400 })
    }

    // Get request body
    const rawBody = await request.text()

    // Verify signature
    const webhookSecret = env.EMAIL_WEBHOOK_SECRET || expectedApiKey
    if (!verifySignature(rawBody, signature, webhookSecret)) {
      logger.warn(`Invalid signature from IP: ${clientIp}`)
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    // Parse and validate request body
    let requestData: RenewalLinkEmailRequest
    try {
      const parsedBody = JSON.parse(rawBody)
      requestData = renewalLinkEmailRequestSchema.parse(parsedBody)
    } catch (error) {
      logger.warn(`Invalid request body from IP: ${clientIp}`, { error })
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
    }

    // Check timestamp to prevent replay attacks (allow 5 minutes tolerance)
    const now = Date.now()
    const requestTime = requestData.timestamp
    const timeDiff = Math.abs(now - requestTime)
    const maxTimeDiff = 5 * 60 * 1000 // 5 minutes

    if (timeDiff > maxTimeDiff) {
      logger.warn(`Request timestamp too old from IP: ${clientIp}. Time diff: ${timeDiff}ms`)
      return NextResponse.json({ error: "Request timestamp too old" }, { status: 400 })
    }

    // Send the email
    const result = await sendRenewalLinkEmailFromSubscription(
      requestData.userEmail,
      requestData.userName,
      requestData.token,
      requestData.subscription,
      new Date(requestData.expiresAt),
      requestData.userLocale
    )

    if (result.success) {
      logger.log(`Renewal link email sent successfully via webhook for subscription ${requestData.subscription.id}`)
      return NextResponse.json({ success: true })
    } else {
      logger.error(`Failed to send renewal link email via webhook: ${result.reason}`)
      return NextResponse.json({ error: "Failed to send email", reason: result.reason }, { status: 500 })
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Webhook error: ${errorMessage}`, { error })

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 })
}

export async function PUT() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 })
}

export async function DELETE() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 })
}
