"use client"

import React, { useState } from "react"
import { toast } from "react-toastify"

import SelectWithSearch from "@/components/ui/searchable-select"
import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { BillingPeriod } from "@prisma/client"

interface CreateAdminSubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

const CreateAdminSubscriptionModal = ({ isOpen, onClose, onSuccess }: CreateAdminSubscriptionModalProps) => {
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [selectedPlanId, setSelectedPlanId] = useState<number>(0)
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>(BillingPeriod.MONTHLY)
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split("T")[0])
  const [endDate, setEndDate] = useState<string>(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
  )
  const [userSearchQuery, setUserSearchQuery] = useState<string>("")

  const { data: users, isLoading: isSearchingUsers } = trpc.user.searchForAdmin.useQuery(
    { query: userSearchQuery },
    { enabled: userSearchQuery.length >= 2 }
  )

  const { data: plans } = trpc.plan.getAll.useQuery()

  const createAdminSubscription = trpc.subscription.createAdminManaged.useMutation({
    onSuccess: () => {
      toast.success("Abonnement créé avec succès")
      onSuccess()
      onClose()
      resetForm()
    },
    onError: (error) => {
      toast.error(`Erreur lors de la création: ${error.message}`)
    },
  })

  const resetForm = () => {
    setSelectedUserId("")
    setSelectedPlanId(0)
    setBillingPeriod(BillingPeriod.MONTHLY)
    setStartDate(new Date().toISOString().split("T")[0])
    setEndDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0])
    setUserSearchQuery("")
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedUserId || !selectedPlanId) {
      toast.error("Veuillez sélectionner un utilisateur et un plan")
      return
    }

    createAdminSubscription.mutate({
      userId: selectedUserId,
      planId: selectedPlanId,
      billingPeriod,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
    })
  }

  const getBillingPeriodLabel = (period: BillingPeriod) => {
    return period === "MONTHLY" ? "Mensuel" : "Annuel"
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        {(onClose) => (
          <form onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              Créer un abonnement
            </ModalHeader>
            <ModalBody>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="md:col-span-2">
                  <SelectWithSearch
                    label="Sélectionner un utilisateur"
                    placeholder="Choisissez un utilisateur"
                    value={selectedUserId}
                    onChange={setSelectedUserId}
                    onSearch={setUserSearchQuery}
                    searchResults={users?.map(user => ({
                      key: user.id,
                      label: `${user.name || user.email} (${user.email})`,
                      name: user.name || undefined
                    }))}
                    isSearching={isSearchingUsers}
                    noResultsMessage="Aucun utilisateur trouvé"
                    searchPlaceholder="Tapez le nom ou l'email..."
                    minSearchLength={2}
                    isRequired
                  />
                </div>

                <div>
                  {
                    <Select
                      label="Plan"
                      selectedKeys={selectedPlanId ? [selectedPlanId.toString()] : []}
                      onChange={(e) => setSelectedPlanId(Number(e.target.value))}
                      isRequired
                    >
                      {plans ? plans?.map((plan) => (
                        <SelectItem key={plan.id.toString()} value={plan.id.toString()} textValue={plan.name}>
                          {plan.name} {!plan.isActive ? "(Inactif)" : ""}
                        </SelectItem>
                      )) : <></>}
                    </Select>
                  }
                </div>

                <div>
                  <Select
                    label="Période de facturation"
                    selectedKeys={[billingPeriod]}
                    onChange={(e) => setBillingPeriod(e.target.value as BillingPeriod)}
                    isRequired
                  >
                    {Object.values(BillingPeriod).map((period) => (
                      <SelectItem key={period} value={period}>
                        {getBillingPeriodLabel(period)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de début"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    isRequired
                  />
                </div>

                <div>
                  <Input
                    type="date"
                    label="Date de fin"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    isRequired
                  />
                </div>
              </div>

              <div className="mt-4 rounded-lg bg-warning-50 p-4 dark:bg-warning-100/20">
                <h4 className="font-medium text-warning-700 dark:text-warning-600">
                  ⚠️ Abonnement géré par l&apos;administrateur
                </h4>
                <ul className="mt-2 text-sm text-warning-600 dark:text-warning-500">
                  <li>• Cet abonnement ne sera pas renouvelé automatiquement</li>
                  <li>• Il peut être lié à des plans inactifs</li>
                  <li>• Il expirera à la date de fin sans tentative de paiement</li>
                  <li>• Aucun enregistrement MangoPay ne sera créé</li>
                </ul>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Annuler
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={createAdminSubscription.isPending}
                isDisabled={!selectedUserId || !selectedPlanId}
              >
                Créer l&apos;abonnement
              </Button>
            </ModalFooter>
          </form>
        )}
      </ModalContent>
    </Modal>
  )
}

export default CreateAdminSubscriptionModal
