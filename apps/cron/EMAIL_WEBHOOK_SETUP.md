# Email Webhook Setup

This document explains how to configure the email webhook system for sending renewal link notifications from the cron job to the main application.

## Overview

The cron job uses a secure webhook to send renewal link emails through the main application's email service. This approach:

- Centralizes email logic in the main app
- Avoids duplicating email dependencies in the cron package
- Provides secure communication between services
- Includes proper error handling and retry logic

## Configuration

### Main App Environment Variables

Add these variables to your main app's `.env` file:

```bash
# Email webhook security
EMAIL_WEBHOOK_API_KEY=your-secure-api-key-here
EMAIL_WEBHOOK_SECRET=your-webhook-secret-here  # Optional, defaults to API key
```

### Cron App Environment Variables

Add these variables to your cron app's `.env` file:

```bash
# Email webhook configuration
EMAIL_WEBHOOK_URL=http://localhost:3000/api/webhooks/email/renewal-link
EMAIL_WEBHOOK_API_KEY=your-secure-api-key-here
```

**Important:** Use the same `EMAIL_WEBHOOK_API_KEY` value in both applications.

## Security Features

The webhook implementation includes several security measures:

### 1. API Key Authentication
- Each request must include a valid API key in the `X-API-Key` header
- Requests with missing or invalid API keys are rejected

### 2. Request Signature Verification
- Each request is signed using HMAC-SHA256
- The signature is sent in the `X-Signature` header
- Prevents request tampering and ensures authenticity

### 3. Timestamp Validation
- Each request includes a timestamp to prevent replay attacks
- Requests older than 5 minutes are rejected

### 4. Rate Limiting
- Maximum 10 requests per minute per IP address
- Prevents abuse and DoS attacks

### 5. Input Validation
- All request data is validated using Zod schemas
- Malformed requests are rejected

## Webhook Endpoint

**URL:** `POST /api/webhooks/email/renewal-link`

**Headers:**
- `Content-Type: application/json`
- `X-API-Key: your-api-key`
- `X-Signature: sha256=signature-hash`

**Request Body:**
```json
{
  "userEmail": "<EMAIL>",
  "userName": "John Doe",
  "userLocale": "fr",
  "token": "renewal-token",
  "subscription": {
    "id": "subscription-id",
    "plan": {
      "name": "Premium Plan"
    },
    "billingPeriod": "MONTHLY"
  },
  "expiresAt": "2024-01-15T10:00:00.000Z",
  "timestamp": 1704441600000
}
```

**Response:**
```json
{
  "success": true
}
```

## Error Handling

The webhook client includes comprehensive error handling:

### Retry Logic
- Automatic retries for server errors (5xx)
- Exponential backoff with maximum 10-second delay
- No retries for client errors (4xx)
- Maximum 3 retry attempts

### Error Types
- **Network errors:** Connection timeouts, DNS failures
- **Server errors:** 5xx HTTP status codes
- **Client errors:** 4xx HTTP status codes (authentication, validation)
- **Configuration errors:** Missing webhook URL or API key

### Logging
All webhook attempts and errors are logged with appropriate detail levels.

## Testing

### Local Development
1. Start the main app: `npm run dev` (port 3000)
2. Set `EMAIL_WEBHOOK_URL=http://localhost:3000/api/webhooks/email/renewal-link`
3. Run the cron job to test webhook communication

### Production
1. Set `EMAIL_WEBHOOK_URL` to your production app URL
2. Use strong, unique API keys
3. Monitor webhook logs for any issues

## Troubleshooting

### Common Issues

1. **"Email webhook not configured"**
   - Check that `EMAIL_WEBHOOK_URL` and `EMAIL_WEBHOOK_API_KEY` are set in cron environment

2. **"Unauthorized" (401)**
   - Verify API keys match between main app and cron app
   - Check that `EMAIL_WEBHOOK_API_KEY` is set in main app environment

3. **"Invalid signature" (401)**
   - Ensure webhook secret is consistent
   - Check for any request body modifications

4. **"Request timestamp too old" (400)**
   - Check system clocks are synchronized
   - Ensure requests are sent promptly

5. **"Rate limit exceeded" (429)**
   - Reduce request frequency
   - Check for duplicate requests

### Debug Mode

Enable debug logging by setting log level to debug in both applications to see detailed webhook communication logs.

## Security Best Practices

1. **Use strong API keys:** Generate cryptographically secure random strings
2. **Rotate keys regularly:** Update API keys periodically
3. **Monitor logs:** Watch for suspicious activity or repeated failures
4. **Use HTTPS:** Always use HTTPS in production environments
5. **Firewall rules:** Restrict webhook endpoint access to known IP ranges if possible

## Migration from TODO

The previous implementation had a TODO comment for email sending. This webhook system replaces that with:

- ✅ Actual email delivery
- ✅ Proper error handling
- ✅ Security measures
- ✅ Retry logic
- ✅ Centralized email templates
- ✅ Logging and monitoring
